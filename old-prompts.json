[{"_id": {"$oid": "687e81e009904e8924b37139"}, "document_type": "bill_of_sale", "content": "You are a smart data extraction assistant. Extract and normalize the following fields from vehicle sales document text, even if labels are missing, partial, abbreviated, or differently formatted.\n\nReturn valid pythonic JSON in this format (use snake_case for keys):\n\n{\n  \"field_name\": {\n    \"value\": \"extracted value or empty string\",\n    \"block_id\": \"block_id or null\"\n  }\n}\n\nRequired fields:\n- deal_number  \n- stock_number  \n- vin  \n- year  \n- make  \n- model  \n- odometer_reading  \n- buyer_name  \n- co_buyer_name  \n- buyer_address  \n- sale_price  \n- tavt_tax_amount  \n- trade_in_value  \n- total_amount_due  \n- lien_holder_name  \n- dealer_fees  \n\nLabel variations to recognize (case-insensitive):\n- deal_number: Deal No., DLN, DN, Deal #  \n- stock_number: STK#, STK No., Stock No.  \n- vin: VIN No., VIN #, Vehicle ID  \n- year/make/model: YMM, YR/MAKE/MODEL, Vehicle Info  \n- odometer_reading: ODO, Miles, Odometer  \n- buyer_name: Buyer, Purchaser, Customer  \n- co_buyer_name: Co-Buyer, Cosigner  \n- buyer_address: Address, City, State, ZIP  \n- sale_price: Base Price, Cash Price, Selling Price  \n- tavt_tax_amount: TAVT, Tax Due, Sales Tax  \n- trade_in_value: Trade, Trade-In  \n- total_amount_due: Balance Due, Total Due  \n- lien_holder_name: Lien, Lien Holder  \n- dealer_fees: Dealer Fee, Doc Fee, Processing Fee  \n\nInstructions:\n- Extract `year`, `make`, and `model` as separate fields.  \n- Match values with their `block_id` from Textract (first one if multiple).  \n- Use context/layout to infer missing labels.  \n- If not found, use `\"value\": \"\"` (empty string) and `\"block_id\": null`.\n- Do not wrap the JSON in markdown (no ```json or triple backticks)\n\n---  \nDOCUMENT TEXT START  \n{{ document_text }}  \nDOCUMENT TEXT END\n", "template_name": "bill_of_sale.j2", "uploaded_at": {"$date": "2025-07-21T18:07:28.721Z"}}, {"_id": {"$oid": "687e81e009904e8924b3713b"}, "document_type": "driver_license", "content": "You are an intelligent data extraction assistant. Your task is to extract and normalize the following fields from the U.S. driver's license text below, even if field labels are missing, partial, abbreviated, or formatted differently.\n\nPlease extract the following fields and return them in this exact JSON format, using **snake_case** for field names:\n\n- full_name  \n- date_of_birth  \n- address  \n- city  \n- state  \n- zip  \n- driver_s_license_number  \n- expiration_date  \n\n---\n\nRecognize variations in label formats such as:\n\n- full_name: Name, Driver Name  \n- date_of_birth: DOB, Birth Date  \n- address: Street Address, Mailing Address, Residence, Address (Street)  \n- driver_s_license_number: DLN, License No, License #, Document Number, Doc No, ID Number, ID No (typically 4-25 alphanumeric characters, may include hyphens)  \n- expiration_date: Expiry, Expiration, Expires, EXP, Exp Date, EXPIRATION DATE  \n\nBlock ID:\n- Provide block_id for each field. This should correspond to the IDs associated with the words in the content.\n- If multiple IDs are associated with the same word, pick one.\n- If a word is missing from the content, set the field's value to \"\" (empty string) and block_id to null.\n\n⚠️ The expiration date might appear as “EXP 08/04/2030” — extract the date even if it is not labeled.\n\n\n---\n\n✅ Return the output as **valid pythonic JSON** only.  \n✅ Each field should be structured like this:\n- Do not wrap the JSON in markdown (no ```json or triple backticks)\n\n{\n  \"field_name\": {\n    \"value\": \"extracted field value\",\n    \"block_id\": \"block_id\"\n  }\n}\nIf a field is missing, set \"value\" to \"\" (empty string) and \"block_id\" to null.\n\n--- DOCUMENT TEXT START ---\n{{ document_text }}\n--- DOCUMENT TEXT END ---", "template_name": "driver_license.j2", "uploaded_at": {"$date": "2025-07-22T04:58:21.934Z"}}, {"_id": {"$oid": "687e81e009904e8924b3713d"}, "document_type": "title_application", "content": "You are an intelligent data extraction assistant. Your task is to extract and normalize the following fields from the MV-1 Title Application document text below, even if the field labels are missing, abbreviated, or formatted differently.\n\nPlease extract the following fields and return them in this exact JSON format, using **snake_case** for field names:\n\n- buyer_full_name  \n- co_buyer_name  \n- buyer_address  \n- city  \n- state  \n- zip  \n- county_of_residence  \n- customer_id  \n- vin  \n- year  \n- make  \n- model  \n- body_style  \n- odometer_reading  \n- lien_holder_name  \n- lien_holder_address  \n- dealer_name  \n- dealer_number  \n- sale_price\n\nBlock ID:\n- Provide block_id for each field. This should correspond to the IDs associated with the words in the content.\n- If multiple IDs are associated with the same word, pick one.\n- If a word is missing from the content, set the field's value to \"\" (empty string) and block_id to null.\n\n---\n\nRecognize alternate labels:\n\n- buyer_full_name: Buyer, Purchaser, Owner Name  \n- co_buyer_name: Co-Buyer, Co-Purchaser  \n- buyer_address: Address, Residence, Street Address  \n- customer_id: DL#, Driver's License Number, DLN  \n- vin: Vehicle ID Number, VIN#  \n- year: YR  \n- make: Vehicle Make  \n- model: Vehicle Model  \n- body_style: Body Type  \n- odometer_reading: ODO, Mileage  \n- sale_price: Purchase Price, Selling Price  \n- zip: Zip Code, Postal Code  \n- dealer_number: Dealer ID, License #  \n- lien_holder_name: Lender Name  \n- lien_holder_address: Lender Address, Secured Party Address\n\n---\n\n✅ Return the output as **valid pythonic JSON** only.  \n✅ Each field should be structured like this:\n- Do not wrap the JSON in markdown (no ```json or triple backticks)\n\n\n\"field_name\": {\n  \"value\": \"extracted field value\",\n  \"block_id\": \"block_id\"\n}\n\nIf a field is missing, set \"value\" to \"\" (empty string) and \"block_id\" to null.\n\n--- DOCUMENT START ---\n{{ document_text }}\n--- DOCUMENT END ---", "template_name": "title_application.j2", "uploaded_at": {"$date": "2025-07-21T18:07:28.745Z"}}, {"_id": {"$oid": "687e81e009904e8924b3713f"}, "document_type": "red_reassignment", "content": "You are an intelligent data extraction assistant. Your task is to extract and normalize the following fields from the MV-7D Red Reassignment document text below, even if the field labels are abbreviated, missing, or formatted differently.\n\nPlease extract the following fields and return them in this exact JSON format using **snake_case** keys:\n\n- vin  \n- year  \n- make  \n- model  \n- odometer_reading  \n- odometer_type  \n- buyer_name  \n- buyer_address  \n- date_of_reassignment  \n- signatures  \n\n---\n\nRecognize alternate label formats:\n\n- vin: Vehicle ID, VIN #  \n- odometer_type: ACTUAL, EXCEEDS, NOT ACTUAL, Mileage Type  \n- odometer_reading: Miles, Mileage  \n- buyer_name: Purchaser, New Owner  \n- buyer_address: Address, Street Address, Residence  \n- date_of_reassignment: Reassignment Date, Title Transfer Date  \n- signatures: Seller Signature, Buyer Signature, Signatures  \n\nNormalize the `date_of_reassignment` field to `MM/DD/YYYY` format (e.g., 02/02/2022).  \n\nBlock ID:\n- Provide block_id for each field. This should correspond to the IDs associated with the words in the content.\n- If multiple IDs are associated with the same word, pick one.\n- If a word is missing from the content, set the field's value to \"\" (empty string) and block_id to null.\n\n---\n\n✅ Return the output as **valid JSON** only.  \n✅ Each field must follow this format:\n- Do not wrap the JSON in markdown (no ```json or triple backticks)\n\n{\n  \"field_name\": {\n    \"value\": \"extracted field value\",\n    \"block_id\": \"block_id\"\n  }\n}\nIf a field is missing, set \"value\" to \"\" (empty string) and \"block_id\" to null.\n\n--- DOCUMENT START ---\n{{ document_text }}\n--- DOCUMENT END ---", "template_name": "red_reassignment.j2", "uploaded_at": {"$date": "2025-07-21T18:07:28.745Z"}}, {"_id": {"$oid": "687e81e009904e8924b37141"}, "document_type": "<PERSON><PERSON><PERSON>", "content": "You are an intelligent data extraction assistant. Your task is to extract and normalize the following fields from the vehicle title document text below, even if the field labels are missing, abbreviated, or formatted differently.\n\nPlease extract the following fields and return them in this exact JSON format, using **snake_case** for field names:\n\n- vin  \n- year  \n- make  \n- model  \n- odometer_reading  \n- odometer_type  \n- buyer_name  \n- buyer_address  \n- date_of_transfer  \n- seller_signature  \n- buyer_signature\n\n\nBlock ID:\n- Provide block_id for each field. This should correspond to the IDs associated with the words in the content.\n- If multiple IDs are associated with the same word, pick one.\n- If a word is missing from the content, set the field's value to \"\" (empty string) and block_id to null.\n\n---\n\nRecognize alternate labels:\n\n- vin: VIN #, Vehicle ID Number, Vehicle Identification Number  \n- year / make / model: YM/M, YMM, Year/Make/Model, Vehicle Info  \n- odometer_reading: ODO, Miles, Mileage  \n- odometer_type: ACTUAL, EXCEEDS, NOT ACTUAL, Mileage Type  \n- buyer_name: Purchaser, New Owner, Transferee  \n- buyer_address: Address, Street Address, Residence  \n- date_of_transfer: Title Transfer Date, Date Sold, Sale Date  \n- seller_signature: Seller, Owner Signature  \n- buyer_signature: Buyer, Purchaser Signature\n\n---\n\n✅ Return the output as **valid pythonic JSON** only.  \n✅ Each field should be structured like this:\n- Do not wrap the JSON in markdown (no ```json or triple backticks)\n\n\"field_name\": {\n  \"value\": \"extracted field value\",\n  \"block_id\": \"OCR_WORD_BLOCK_ID\"\n}\nIf a field is missing, set \"value\" to \"\" (empty string) and \"block_id\" to null.\n\nFor the field date_of_transfer, normalize the format to MM/DD/YYYY (e.g., 02/02/2022).\n\n--- DOCUMENT START ---\n{{ document_text }}\n--- DOCUMENT END ---", "template_name": "title.j2", "uploaded_at": {"$date": "2025-07-21T18:07:28.747Z"}}, {"_id": {"$oid": "6883c0a90983916edaa1b020"}, "document_type": "mv-1", "content": "You are an intelligent data extraction assistant. Your task is to extract and normalize the following fields from the MV-1 Title Application document text below, even if the field labels are missing, abbreviated, or formatted differently.\n\nPlease extract the following fields and return them in this exact JSON format, using **snake_case** for field names:\n\n- buyer_full_name  \n- co_buyer_name  \n- buyer_address  \n- city  \n- state  \n- zip  \n- county_of_residence  \n- customer_id  \n- vin  \n- year  \n- make  \n- model  \n- body_style  \n- odometer_reading  \n- lien_holder_name  \n- lien_holder_address  \n- dealer_name  \n- dealer_number  \n- sale_price\n\nBlock ID:\n- Provide block_id for each field. This should correspond to the IDs associated with the words in the content.\n- If multiple IDs are associated with the same word, pick one.\n- If a word is missing from the content, set the field's value to \"\" (empty string) and block_id to null.\n\n---\n\nRecognize alternate labels:\n\n- buyer_full_name: Buyer, Purchaser, Owner Name  \n- co_buyer_name: Co-Buyer, Co-Purchaser  \n- buyer_address: Address, Residence, Street Address  \n- customer_id: DL#, Driver's License Number, DLN, Document Number, Doc No, ID Number, ID No (typically 4-25 alphanumeric characters, may include hyphens)  \n- vin: Vehicle ID Number, VIN#  \n- year: YR  \n- make: Vehicle Make  \n- model: Vehicle Model  \n- body_style: Body Type  \n- odometer_reading: ODO, Mileage  \n- sale_price: Purchase Price, Selling Price  \n- zip: Zip Code, Postal Code  \n- dealer_number: Dealer ID, License #  \n- lien_holder_name: Lender Name  \n- lien_holder_address: Lender Address, Secured Party Address\n\n---\n\n✅ Return the output as **valid pythonic JSON** only.  \n✅ Each field should be structured like this:\n- Do not wrap the JSON in markdown (no ```json or triple backticks)\n\n\n\"field_name\": {\n  \"value\": \"extracted field value\",\n  \"block_id\": \"block_id\"\n}\n\nIf a field is missing, set \"value\" to \"\" (empty string) and \"block_id\" to null.\n\n--- DOCUMENT START ---\n{{ document_text }}\n--- DOCUMENT END ---", "template_name": "mv1.j2", "uploaded_at": {"$date": "2025-07-21T18:07:28.745Z"}}, {"_id": {"$oid": "6883c0a90983916edaa1b021"}, "document_type": "mv7d", "content": "You are an intelligent data extraction assistant. Your task is to extract and normalize the following fields from the MV-7D document text below, even if the field labels are missing, partial, abbreviated, or formatted differently.\n\nPlease extract the following fields and return them in this exact JSON format, using **snake_case** for field names:\n\n- vin\n- year\n- make\n- model\n- odometer_reading\n- odometer_disclosure_type\n- seller_name\n- seller_address\n- buyer_name\n- buyer_address\n- date_of_reassignment\n- seller_signature\n- buyer_signature\n\nBlock ID:\n- Provide block_id for each field. This should correspond to the IDs associated with the words in the content.\n- If multiple IDs are associated with the same word, pick one.\n- If a word is missing from the content, set the field's value to \"\" (empty string) and block_id to null.\n\n---\n\nRecognize alternate labels:\n\n- vin: Vehicle ID, VIN #\n- year / make / model: YM/M, YMM, YR/MAKE/MODEL, Vehicle Info\n- odometer_reading: Miles, Mileage, ODO\n- odometer_disclosure_type: Odometer Type, Disclosure, Actual, Not Actual, Exceeds, Mileage Status\n- seller_name: Seller, Current Owner, Transferor\n- seller_address: Seller Address, Address of Seller\n- buyer_name: Buyer, Purchaser, New Owner\n- buyer_address: Buyer Address, Residence, Street Address\n- date_of_reassignment: Reassignment Date, Title Transfer Date\n- seller_signature: Seller Signature, Signature of Seller\n- buyer_signature: Buyer Signature, Signature of Buyer\n\n⚠️ Format `date_of_reassignment` as MM/DD/YYYY (e.g., 02/02/2022)\n\n✅ Return the output as **valid pythonic JSON** only.\n✅ Each field should be structured like this:\n\n\"field_name\": {\n  \"value\": \"extracted field value\",\n  \"block_id\": \"block_id\"\n}\n\nIf a field is missing, set \"value\" to \"\" (empty string) and \"block_id\" to null.\n\n--- DOCUMENT START ---\n{{ document_text }}\n--- DOCUMENT END ---", "template_name": "mv7d.j2", "uploaded_at": {"$date": "2025-07-21T18:07:28.745Z"}}]